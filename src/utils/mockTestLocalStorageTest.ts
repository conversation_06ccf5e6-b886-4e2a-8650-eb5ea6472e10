// Test file to verify mock test and D-Day local storage functionality
import { enhancedMockTestUtils, dDayStorage, dDayIntegration, upcomingTestStorage } from './mockTestLocalStorage';
import { MockTest, UpcomingTest } from '../types/mockTest';

// Test data
const testUserId = 'test-user-123';

const sampleMockTest: MockTest = {
  id: 'test-mock-1',
  name: 'Sample Physics Test',
  date: '2024-01-15',
  subjectMarks: [
    {
      subject: 'Physics',
      subjectColor: '#3b82f6',
      marksObtained: 85,
      totalMarks: 100
    }
  ],
  totalMarksObtained: 85,
  totalMarks: 100,
  notes: 'Good performance in mechanics',
  createdAt: new Date().toISOString(),
  userId: testUserId
};

const sampleUpcomingTest: UpcomingTest = {
  id: 'upcoming-test-1',
  name: 'Chemistry Mock Test',
  date: '2024-02-01',
  time: '10:00',
  userId: testUserId,
  categoryId: 'chemistry-cat',
  syllabus: ['Organic Chemistry', 'Inorganic Chemistry'],
  isNotificationEnabled: true,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString()
};

// Test functions
export const testMockTestLocalStorage = () => {
  console.log('🧪 Testing Mock Test Local Storage...');
  
  try {
    // Clear existing data
    enhancedMockTestUtils.delete(testUserId, sampleMockTest.id);
    
    // Test save
    console.log('📝 Testing save...');
    const savedTest = enhancedMockTestUtils.save(testUserId, sampleMockTest);
    console.log('✅ Save successful:', savedTest.name);
    
    // Test get all
    console.log('📋 Testing get all...');
    const allTests = enhancedMockTestUtils.getAll(testUserId);
    console.log('✅ Get all successful:', allTests.length, 'tests found');
    
    // Test update
    console.log('✏️ Testing update...');
    const updatedTest = enhancedMockTestUtils.update(testUserId, sampleMockTest.id, {
      name: 'Updated Physics Test',
      notes: 'Updated notes'
    });
    console.log('✅ Update successful:', updatedTest?.name);
    
    // Test delete
    console.log('🗑️ Testing delete...');
    const deleteSuccess = enhancedMockTestUtils.delete(testUserId, sampleMockTest.id);
    console.log('✅ Delete successful:', deleteSuccess);
    
    console.log('✅ Mock Test Local Storage tests passed!');
    return true;
  } catch (error) {
    console.error('❌ Mock Test Local Storage test failed:', error);
    return false;
  }
};

export const testDDayLocalStorage = () => {
  console.log('🧪 Testing D-Day Local Storage...');
  
  try {
    // Clear existing data
    dDayStorage.clear(testUserId);
    
    // Test upcoming test sync
    console.log('📝 Testing upcoming test sync...');
    const syncedExam = dDayIntegration.syncWithDDay(sampleUpcomingTest);
    console.log('✅ Sync successful:', syncedExam.name);
    
    // Test get upcoming for D-Day
    console.log('📋 Testing get upcoming for D-Day...');
    const upcomingExams = dDayIntegration.getUpcomingForDDay(testUserId, 30);
    console.log('✅ Get upcoming successful:', upcomingExams.length, 'exams found');
    
    // Test direct D-Day exam creation
    console.log('➕ Testing direct D-Day exam creation...');
    const directExam = dDayStorage.save(testUserId, {
      id: 'direct-exam-1',
      name: 'Direct Math Exam',
      date: '2024-02-15',
      time: '14:00',
      userId: testUserId,
      status: 'upcoming',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    });
    console.log('✅ Direct exam creation successful:', directExam.name);
    
    // Test get all D-Day exams
    console.log('📋 Testing get all D-Day exams...');
    const allExams = dDayStorage.getAll(testUserId);
    console.log('✅ Get all exams successful:', allExams.length, 'exams found');
    
    // Test delete
    console.log('🗑️ Testing delete...');
    const deleteSuccess = dDayIntegration.removeDDayExam(testUserId, sampleUpcomingTest.id);
    console.log('✅ Delete successful:', deleteSuccess);
    
    // Clean up
    dDayStorage.clear(testUserId);
    
    console.log('✅ D-Day Local Storage tests passed!');
    return true;
  } catch (error) {
    console.error('❌ D-Day Local Storage test failed:', error);
    return false;
  }
};

export const runAllTests = () => {
  console.log('🚀 Running all local storage tests...');
  
  const mockTestResult = testMockTestLocalStorage();
  const dDayResult = testDDayLocalStorage();
  
  if (mockTestResult && dDayResult) {
    console.log('🎉 All tests passed! Local storage is working correctly.');
  } else {
    console.log('❌ Some tests failed. Please check the implementation.');
  }
  
  return mockTestResult && dDayResult;
};

// Export for use in browser console
if (typeof window !== 'undefined') {
  (window as any).mockTestLocalStorageTest = {
    testMockTestLocalStorage,
    testDDayLocalStorage,
    runAllTests
  };
}
