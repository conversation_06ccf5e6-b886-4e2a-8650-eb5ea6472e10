import { useState } from "react";
import { motion } from "framer-motion";
import { 
  Plus, 
  Calendar, 
  Clock, 
  Target,
  BookOpen,
  Bell,
  Save,
  X
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { TestCategory, UpcomingTest } from "@/types/mockTest";
import { DDayExam } from "@/utils/mockTestLocalStorage";
import { useSupabaseAuth } from "@/contexts/SupabaseAuthContext";
import { 
  upcomingTestStorage, 
  dDayStorage, 
  dDayIntegration 
} from "@/utils/mockTestLocalStorage";
import { toast } from "@/components/ui/use-toast";

interface TestQuickActionsProps {
  onTestAdded?: () => void;
  categories: TestCategory[];
}

export function TestQuickActions({ onTestAdded, categories }: TestQuickActionsProps) {
  const { user } = useSupabaseAuth();
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isAddingDirectly, setIsAddingDirectly] = useState(false);
  
  // Form state
  const [formData, setFormData] = useState({
    name: "",
    date: new Date(),
    time: "09:00",
    categoryId: "",
    description: "",
    syllabus: [] as string[],
    testPaperUrl: "",
    isNotificationEnabled: true,
    priority: "medium" as DDayExam['priority'],
    addToBoth: true, // Add to both upcoming tests and D-Day
  });

  const [syllabusInput, setSyllabusInput] = useState("");

  const resetForm = () => {
    setFormData({
      name: "",
      date: new Date(),
      time: "09:00",
      categoryId: "",
      description: "",
      syllabus: [],
      testPaperUrl: "",
      isNotificationEnabled: true,
      priority: "medium",
      addToBoth: true,
    });
    setSyllabusInput("");
  };

  const addSyllabusTopic = () => {
    if (syllabusInput.trim() && !formData.syllabus.includes(syllabusInput.trim())) {
      setFormData(prev => ({
        ...prev,
        syllabus: [...prev.syllabus, syllabusInput.trim()]
      }));
      setSyllabusInput("");
    }
  };

  const removeSyllabusTopic = (topic: string) => {
    setFormData(prev => ({
      ...prev,
      syllabus: prev.syllabus.filter(t => t !== topic)
    }));
  };

  const handleSubmit = async () => {
    if (!user?.id) {
      toast({
        title: "Error",
        description: "You must be logged in to add tests",
        variant: "destructive",
      });
      return;
    }

    if (!formData.name.trim()) {
      toast({
        title: "Error",
        description: "Test name is required",
        variant: "destructive",
      });
      return;
    }

    setIsAddingDirectly(true);

    try {
      const testId = `test_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      const dateStr = format(formData.date, "yyyy-MM-dd");

      if (formData.addToBoth) {
        // Add to upcoming tests
        const upcomingTest: UpcomingTest = {
          id: testId,
          name: formData.name,
          date: dateStr,
          time: formData.time,
          categoryId: formData.categoryId || undefined,
          syllabus: formData.syllabus,
          testPaperUrl: formData.testPaperUrl || undefined,
          isNotificationEnabled: formData.isNotificationEnabled,
          userId: user.id,
          createdAt: new Date().toISOString(),
        };

        const savedTest = upcomingTestStorage.create(upcomingTest);

        // Sync with D-Day
        dDayIntegration.syncWithDDay(savedTest);

        toast({
          title: "Success",
          description: "Test added to both upcoming tests and D-Day countdown",
        });
      } else {
        // Add directly to D-Day only
        const dDayExam: DDayExam = {
          id: testId,
          name: formData.name,
          date: dateStr,
          time: formData.time,
          userId: user.id,
          status: 'upcoming',
          priority: formData.priority,
          category: formData.categoryId,
          description: formData.description,
          reminderSettings: {
            enabled: formData.isNotificationEnabled,
            intervals: [7, 3, 1],
          },
          preparationData: {
            studyHours: 0,
            completedTopics: [],
            totalTopics: formData.syllabus,
            confidenceLevel: 3,
          },
          syncedFromUpcoming: false,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };

        dDayStorage.save(user.id, dDayExam);

        toast({
          title: "Success",
          description: "Test added to D-Day countdown",
        });
      }

      setIsAddDialogOpen(false);
      resetForm();
      onTestAdded?.();
    } catch (error) {
      console.error('Error adding test:', error);
      toast({
        title: "Error",
        description: "Failed to add test",
        variant: "destructive",
      });
    } finally {
      setIsAddingDirectly(false);
    }
  };

  return (
    <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
      <DialogTrigger asChild>
        <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
          <Button className="gap-2 bg-gradient-to-r from-violet-600 to-purple-600 hover:from-violet-700 hover:to-purple-700 text-white shadow-lg">
            <Plus className="h-4 w-4" />
            Add Test
          </Button>
        </motion.div>
      </DialogTrigger>
      
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Target className="h-5 w-5 text-violet-600" />
            Add New Test
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <div>
              <Label htmlFor="name">Test Name *</Label>
              <Input
                id="name"
                placeholder="Enter test name..."
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label>Date *</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !formData.date && "text-muted-foreground"
                      )}
                    >
                      <Calendar className="mr-2 h-4 w-4" />
                      {formData.date ? format(formData.date, "PPP") : "Pick a date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <CalendarComponent
                      mode="single"
                      selected={formData.date}
                      onSelect={(date) => date && setFormData(prev => ({ ...prev, date }))}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>

              <div>
                <Label htmlFor="time">Time</Label>
                <Input
                  id="time"
                  type="time"
                  value={formData.time}
                  onChange={(e) => setFormData(prev => ({ ...prev, time: e.target.value }))}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label>Category</Label>
                <Select value={formData.categoryId} onValueChange={(value) => setFormData(prev => ({ ...prev, categoryId: value }))}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label>Priority</Label>
                <Select value={formData.priority} onValueChange={(value: DDayExam['priority']) => setFormData(prev => ({ ...prev, priority: value }))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">Low</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                    <SelectItem value="critical">Critical</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* Description */}
          <div>
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              placeholder="Add any additional details about the test..."
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              rows={3}
            />
          </div>

          {/* Syllabus */}
          <div>
            <Label>Syllabus Topics</Label>
            <div className="space-y-3">
              <div className="flex gap-2">
                <Input
                  placeholder="Add a topic..."
                  value={syllabusInput}
                  onChange={(e) => setSyllabusInput(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && addSyllabusTopic()}
                />
                <Button type="button" onClick={addSyllabusTopic} variant="outline">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              
              {formData.syllabus.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {formData.syllabus.map((topic, index) => (
                    <Badge key={index} variant="secondary" className="gap-1">
                      {topic}
                      <button
                        type="button"
                        onClick={() => removeSyllabusTopic(topic)}
                        className="ml-1 hover:text-red-500"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </Badge>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Test Paper URL */}
          <div>
            <Label htmlFor="testPaperUrl">Test Paper URL</Label>
            <Input
              id="testPaperUrl"
              placeholder="https://..."
              value={formData.testPaperUrl}
              onChange={(e) => setFormData(prev => ({ ...prev, testPaperUrl: e.target.value }))}
            />
          </div>

          {/* Settings */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Bell className="h-4 w-4 text-muted-foreground" />
                <Label htmlFor="notifications">Enable Notifications</Label>
              </div>
              <Switch
                id="notifications"
                checked={formData.isNotificationEnabled}
                onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isNotificationEnabled: checked }))}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Target className="h-4 w-4 text-muted-foreground" />
                <Label htmlFor="addToBoth">Add to both Upcoming Tests & D-Day</Label>
              </div>
              <Switch
                id="addToBoth"
                checked={formData.addToBoth}
                onCheckedChange={(checked) => setFormData(prev => ({ ...prev, addToBoth: checked }))}
              />
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4">
            <Button
              variant="outline"
              onClick={() => setIsAddDialogOpen(false)}
              className="flex-1"
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={isAddingDirectly || !formData.name.trim()}
              className="flex-1 bg-gradient-to-r from-violet-600 to-purple-600 hover:from-violet-700 hover:to-purple-700"
            >
              {isAddingDirectly ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                  Adding...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Add Test
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
