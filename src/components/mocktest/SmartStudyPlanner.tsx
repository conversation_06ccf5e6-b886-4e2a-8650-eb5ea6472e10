import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { 
  Calendar, 
  Clock, 
  BookOpen, 
  Target,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Lightbulb,
  Zap,
  Brain,
  Timer,
  Award
} from "lucide-react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { DDayExam, chapterProgressUtils } from "@/utils/mockTestLocalStorage";
import {
  ExamInsightsEngine,
  PreparationInsight,
  ExamNotificationSystem
} from "@/utils/examNotifications";
import { cn } from "@/lib/utils";
import { format } from "date-fns";

interface SmartStudyPlannerProps {
  exams: DDayExam[];
  selectedExam?: DDayExam;
  onExamSelect?: (exam: DDayExam) => void;
}

export function SmartStudyPlanner({ exams, selectedExam, onExamSelect }: SmartStudyPlannerProps) {
  const [insights, setInsights] = useState<PreparationInsight[]>([]);
  const [studySchedule, setStudySchedule] = useState<{ date: string; topics: string[]; hours: number }[]>([]);
  const [activeTab, setActiveTab] = useState("insights");

  useEffect(() => {
    // Generate insights for all exams
    const generatedInsights = ExamInsightsEngine.generateInsights(exams);
    setInsights(generatedInsights);

    // Generate study schedule for selected exam
    if (selectedExam) {
      const schedule = ExamInsightsEngine.generateStudySchedule(selectedExam);
      setStudySchedule(schedule);
    }
  }, [exams, selectedExam]);

  const getInsightIcon = (type: PreparationInsight['type']) => {
    switch (type) {
      case 'strength': return CheckCircle;
      case 'weakness': return AlertTriangle;
      case 'recommendation': return Lightbulb;
      case 'warning': return AlertTriangle;
      default: return Target;
    }
  };

  const getInsightColor = (type: PreparationInsight['type'], priority: PreparationInsight['priority']) => {
    if (type === 'warning' || priority === 'high') {
      return 'from-red-50 to-red-100 dark:from-red-950/30 dark:to-red-900/20 border-red-200 dark:border-red-800 text-red-700 dark:text-red-400';
    }
    if (type === 'weakness' || priority === 'medium') {
      return 'from-orange-50 to-orange-100 dark:from-orange-950/30 dark:to-orange-900/20 border-orange-200 dark:border-orange-800 text-orange-700 dark:text-orange-400';
    }
    if (type === 'strength') {
      return 'from-green-50 to-green-100 dark:from-green-950/30 dark:to-green-900/20 border-green-200 dark:border-green-800 text-green-700 dark:text-green-400';
    }
    return 'from-blue-50 to-blue-100 dark:from-blue-950/30 dark:to-blue-900/20 border-blue-200 dark:border-blue-800 text-blue-700 dark:text-blue-400';
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.5 }
    }
  };

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="space-y-6"
    >
      {/* Header */}
      <motion.div variants={itemVariants} className="flex items-center justify-between">
        <div>
          <h3 className="text-2xl font-bold bg-gradient-to-r from-violet-600 via-purple-600 to-rose-600 bg-clip-text text-transparent">
            Smart Study Planner
          </h3>
          <p className="text-muted-foreground mt-1">
            AI-powered insights and personalized study recommendations
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <Badge variant="secondary" className="gap-1">
            <Brain className="h-3 w-3" />
            AI Powered
          </Badge>
        </div>
      </motion.div>

      {/* Exam Selector */}
      {exams.length > 1 && (
        <motion.div variants={itemVariants}>
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg flex items-center gap-2">
                <Target className="h-5 w-5 text-violet-600" />
                Select Exam for Detailed Planning
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                {exams.map((exam) => {
                  const daysUntil = Math.ceil((new Date(exam.date).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24));
                  const completionRate = chapterProgressUtils.calculateOverallProgress(exam.preparationData.chapters) / 100;
                  
                  return (
                    <Button
                      key={exam.id}
                      variant={selectedExam?.id === exam.id ? "default" : "outline"}
                      onClick={() => onExamSelect?.(exam)}
                      className={cn(
                        "h-auto p-4 flex flex-col items-start gap-2 transition-all duration-200",
                        selectedExam?.id === exam.id
                          ? "bg-violet-600 hover:bg-violet-700 text-white border-violet-600"
                          : "bg-background hover:bg-muted border-border text-foreground hover:text-foreground"
                      )}
                    >
                      <div className="flex items-center justify-between w-full">
                        <span className="font-medium truncate">{exam.name}</span>
                        <Badge
                          variant="secondary"
                          className={cn(
                            "text-xs",
                            selectedExam?.id === exam.id
                              ? "bg-white/20 text-white border-white/30"
                              : "bg-muted text-muted-foreground border-border"
                          )}
                        >
                          {daysUntil}d
                        </Badge>
                      </div>
                      <Progress
                        value={completionRate * 100}
                        className="w-full h-2"
                      />
                      <span className={cn(
                        "text-xs",
                        selectedExam?.id === exam.id
                          ? "text-white/80"
                          : "text-muted-foreground"
                      )}>
                        {Math.round(completionRate * 100)}% prepared
                      </span>
                    </Button>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}

      {/* Main Content */}
      <motion.div variants={itemVariants}>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="insights">AI Insights</TabsTrigger>
            <TabsTrigger value="schedule">Study Schedule</TabsTrigger>
            <TabsTrigger value="progress">Progress Tracking</TabsTrigger>
          </TabsList>

          <TabsContent value="insights" className="space-y-4">
            {insights.length === 0 ? (
              <Card>
                <CardContent className="flex flex-col items-center justify-center py-12">
                  <Brain className="h-16 w-16 text-muted-foreground mb-4" />
                  <h3 className="text-xl font-semibold mb-2">No Insights Available</h3>
                  <p className="text-muted-foreground text-center">
                    Add some exams and preparation data to get AI-powered insights
                  </p>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-4">
                <AnimatePresence>
                  {insights.map((insight, index) => {
                    const Icon = getInsightIcon(insight.type);
                    const colorClasses = getInsightColor(insight.type, insight.priority);
                    
                    return (
                      <motion.div
                        key={insight.title}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        exit={{ opacity: 0, x: 20 }}
                        transition={{ delay: index * 0.1 }}
                      >
                        <Alert className={cn("bg-gradient-to-r", colorClasses)}>
                          <Icon className="h-5 w-5" />
                          <div className="flex-1">
                            <div className="flex items-center justify-between mb-2">
                              <h4 className="font-semibold">{insight.title}</h4>
                              <Badge 
                                variant="secondary" 
                                className={cn(
                                  "text-xs",
                                  insight.priority === 'high' && "bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400",
                                  insight.priority === 'medium' && "bg-orange-100 text-orange-700 dark:bg-orange-900/30 dark:text-orange-400",
                                  insight.priority === 'low' && "bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400"
                                )}
                              >
                                {insight.priority.toUpperCase()}
                              </Badge>
                            </div>
                            <AlertDescription className="text-sm">
                              {insight.description}
                            </AlertDescription>
                            {insight.actionable && (
                              <Button 
                                variant="outline" 
                                size="sm" 
                                className="mt-3"
                                onClick={() => {
                                  if (insight.examId) {
                                    const exam = exams.find(e => e.id === insight.examId);
                                    if (exam) onExamSelect?.(exam);
                                  }
                                }}
                              >
                                Take Action
                              </Button>
                            )}
                          </div>
                        </Alert>
                      </motion.div>
                    );
                  })}
                </AnimatePresence>
              </div>
            )}
          </TabsContent>

          <TabsContent value="schedule" className="space-y-4">
            {!selectedExam ? (
              <Card>
                <CardContent className="flex flex-col items-center justify-center py-12">
                  <Calendar className="h-16 w-16 text-muted-foreground mb-4" />
                  <h3 className="text-xl font-semibold mb-2">Select an Exam</h3>
                  <p className="text-muted-foreground text-center">
                    Choose an exam above to see your personalized study schedule
                  </p>
                </CardContent>
              </Card>
            ) : studySchedule.length === 0 ? (
              <Card>
                <CardContent className="flex flex-col items-center justify-center py-12">
                  <CheckCircle className="h-16 w-16 text-green-500 mb-4" />
                  <h3 className="text-xl font-semibold mb-2">All Set!</h3>
                  <p className="text-muted-foreground text-center">
                    You've completed all topics for {selectedExam.name}. Time for review!
                  </p>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Calendar className="h-5 w-5 text-violet-600" />
                      Study Schedule for {selectedExam.name}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {studySchedule.map((day, index) => (
                        <motion.div
                          key={day.date}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: index * 0.1 }}
                          className="p-4 rounded-lg border bg-gradient-to-r from-violet-50 to-purple-50 dark:from-violet-950/30 dark:to-purple-950/30"
                        >
                          <div className="flex items-center justify-between mb-3">
                            <div className="flex items-center gap-3">
                              <div className="p-2 rounded-lg bg-violet-600/10">
                                <Calendar className="h-4 w-4 text-violet-600" />
                              </div>
                              <div>
                                <h4 className="font-semibold">
                                  {format(new Date(day.date), 'EEEE, MMM d')}
                                </h4>
                                <p className="text-sm text-muted-foreground">
                                  Day {index + 1} of {studySchedule.length}
                                </p>
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              <Clock className="h-4 w-4 text-muted-foreground" />
                              <span className="font-medium">{day.hours}h</span>
                            </div>
                          </div>
                          
                          <div className="space-y-2">
                            <h5 className="font-medium text-sm">Topics to cover:</h5>
                            <div className="flex flex-wrap gap-2">
                              {day.topics.map((topic, topicIndex) => (
                                <Badge key={topicIndex} variant="secondary" className="text-xs">
                                  {topic}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}
          </TabsContent>

          <TabsContent value="progress" className="space-y-4">
            {!selectedExam ? (
              <Card>
                <CardContent className="flex flex-col items-center justify-center py-12">
                  <TrendingUp className="h-16 w-16 text-muted-foreground mb-4" />
                  <h3 className="text-xl font-semibold mb-2">Select an Exam</h3>
                  <p className="text-muted-foreground text-center">
                    Choose an exam to track your preparation progress
                  </p>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-6">
                {/* Progress Overview */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <TrendingUp className="h-5 w-5 text-violet-600" />
                      Progress Overview: {selectedExam.name}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {/* Chapter Completion */}
                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <span className="font-medium">Chapter Progress</span>
                        <span className="text-sm text-muted-foreground">
                          {chapterProgressUtils.getCompletionStatus(selectedExam.preparationData.chapters).completed} / {chapterProgressUtils.getCompletionStatus(selectedExam.preparationData.chapters).total}
                        </span>
                      </div>
                      <Progress
                        value={chapterProgressUtils.calculateOverallProgress(selectedExam.preparationData.chapters)}
                        className="h-3"
                      />
                    </div>

                    {/* Study Hours */}
                    <div className="grid grid-cols-2 gap-4">
                      <div className="text-center p-4 rounded-lg bg-violet-50 dark:bg-violet-950/20">
                        <Clock className="h-8 w-8 text-violet-600 mx-auto mb-2" />
                        <p className="text-2xl font-bold text-violet-600">{selectedExam.preparationData.studyHours}</p>
                        <p className="text-sm text-muted-foreground">Hours Studied</p>
                      </div>
                      
                      <div className="text-center p-4 rounded-lg bg-emerald-50 dark:bg-emerald-950/20">
                        <Award className="h-8 w-8 text-emerald-600 mx-auto mb-2" />
                        <p className="text-2xl font-bold text-emerald-600">
                          {selectedExam.preparationData.chapters.length > 0
                            ? Math.round(selectedExam.preparationData.chapters.reduce((sum, ch) => sum + ch.confidenceLevel, 0) / selectedExam.preparationData.chapters.length * 10) / 10
                            : 0}/5
                        </p>
                        <p className="text-sm text-muted-foreground">Avg Confidence</p>
                      </div>
                    </div>

                    {/* Chapters Needing Attention */}
                    {(() => {
                      const chaptersNeedingAttention = chapterProgressUtils.getChaptersNeedingAttention(selectedExam.preparationData.chapters);
                      return chaptersNeedingAttention.length > 0 && (
                        <div>
                          <h4 className="font-medium mb-3">Chapters Needing Attention</h4>
                          <div className="space-y-2">
                            {chaptersNeedingAttention.map((chapter, index) => (
                              <div key={index} className="flex items-center justify-between p-2 rounded-lg bg-orange-50 dark:bg-orange-950/20 border border-orange-200 dark:border-orange-800">
                                <span className="text-sm font-medium">{chapter.chapterName}</span>
                                <div className="flex items-center gap-2">
                                  <Badge variant="outline" className="text-xs">
                                    {chapter.completionPercentage}% complete
                                  </Badge>
                                  <Badge variant="outline" className="text-xs">
                                    Confidence: {chapter.confidenceLevel}/5
                                  </Badge>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      );
                    })()}
                  </CardContent>
                </Card>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </motion.div>
    </motion.div>
  );
}
