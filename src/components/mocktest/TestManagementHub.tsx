import { useState, useEffect, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Calendar,
  Clock,
  Plus,
  Filter,
  Search,
  BarChart3,
  Target,
  Zap,
  AlertTriangle,
  CheckCircle,
  Timer,
  BookOpen,
  TrendingUp,
  ChevronDown
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Checkbox } from "@/components/ui/checkbox";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { useSupabaseAuth } from "@/contexts/SupabaseAuthContext";
import { UpcomingTest, TestCategory } from "@/types/mockTest";
import {
  dDayStorage,
  dDayIntegration,
  upcomingTestStorage,
  categoryStorage,
  DDayExam,
  chapterProgressUtils
} from "@/utils/mockTestLocalStorage";
import { toast } from "@/components/ui/use-toast";
import { cn } from "@/lib/utils";
import { DDayCountdownWidget } from "./DDayCountdownWidget";
import { EnhancedUpcomingTestCard } from "./EnhancedUpcomingTestCard";
import { TestQuickActions } from "./TestQuickActions";
import { TestInsightsPanel } from "./TestInsightsPanel";
import { SmartStudyPlanner } from "./SmartStudyPlanner";
import { NotificationCenter } from "./NotificationCenter";
import { ExamCalendarView } from "./ExamCalendarView";
import { EditDDayExamDialog } from "./EditDDayExamDialog";

interface TestManagementHubProps {
  // No props needed for now
}

export function TestManagementHub({}: TestManagementHubProps) {
  const { user } = useSupabaseAuth();
  
  // State management
  const [dDayExams, setDDayExams] = useState<DDayExam[]>([]);
  const [upcomingTests, setUpcomingTests] = useState<UpcomingTest[]>([]);
  const [categories, setCategories] = useState<TestCategory[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [filterPriority, setFilterPriority] = useState<string>("all");
  const [filterStatus, setFilterStatus] = useState<string>("all");
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [activeTab, setActiveTab] = useState("overview");
  const [selectedExam, setSelectedExam] = useState<DDayExam | null>(null);
  const [editingExam, setEditingExam] = useState<DDayExam | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

  // Load data
  const loadData = useCallback(async () => {
    if (!user?.id) return;
    
    setLoading(true);
    try {
      // Load all data
      const [exams, tests, cats] = await Promise.all([
        Promise.resolve(dDayIntegration.getUpcomingForDDay(user.id, 365)),
        Promise.resolve(upcomingTestStorage.getUpcoming(user.id, 365)),
        Promise.resolve(categoryStorage.getAll(user.id))
      ]);

      setDDayExams(exams);
      setUpcomingTests(tests);
      setCategories(cats);
    } catch (error) {
      console.error('Error loading test data:', error);
      toast({
        title: "Error",
        description: "Failed to load test data",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [user?.id]);

  useEffect(() => {
    loadData();
  }, [loadData]);

  // Edit exam handler
  const handleEditExam = (exam: DDayExam) => {
    setEditingExam(exam);
    setIsEditDialogOpen(true);
  };

  // Delete exam handler
  const handleDeleteExam = async (examId: string) => {
    if (!user?.id) return;
  
    const exam = dDayExams.find(e => e.id === examId);
    if (!exam) {
      toast({
        title: "Warning",
        description: "Could not find exam to delete. It might have been already removed.",
        variant: "default",
      });
      return;
    }
  
    const confirmed = window.confirm(
      `Are you sure you want to delete "${exam.name}"? This action cannot be undone.`
    );
  
    if (!confirmed) return;
  
    try {
      let dDaySuccess = false;
      let upcomingSuccess = false;
  
      if (exam.syncedFromUpcoming) {
        // This exam originated from the upcoming tests list.
        // It must be deleted from there.
        upcomingSuccess = upcomingTestStorage.delete(exam.id);
        
        // It might also exist in dDayStorage if it was ever edited/saved.
        // We can attempt to delete it from there too.
        dDaySuccess = dDayStorage.delete(user.id, examId);
      } else {
        // This is a D-Day only exam.
        dDaySuccess = dDayStorage.delete(user.id, examId);
      }
  
      if (dDaySuccess || upcomingSuccess) {
        await loadData();
        toast({
          title: "Success",
          description: "Exam deleted successfully",
        });
      } else {
        console.warn(`Attempted to delete an exam that was not found: ${examId}`);
        toast({
          title: "Warning",
          description: "Could not find the exam to delete. It might have been already removed.",
          variant: "default",
        });
        // Still reload data to ensure UI consistency
        await loadData();
      }
    } catch (error) {
      console.error('Error deleting exam:', error);
      toast({
        title: "Error",
        description: `An unexpected error occurred: ${error.message}`,
        variant: "destructive",
      });
    }
  };

  // Filter and search logic
  const filteredExams = dDayExams.filter(exam => {
    const matchesSearch = exam.name.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesPriority = filterPriority === "all" || exam.priority === filterPriority;
    const matchesStatus = filterStatus === "all" || exam.status === filterStatus;
    const matchesCategories = selectedCategories.length === 0 ||
      (exam.category && selectedCategories.includes(exam.category));

    return matchesSearch && matchesPriority && matchesStatus && matchesCategories;
  });

  // Get statistics
  const stats = dDayStorage.getStats(user?.id || '');
  const syncStatus = dDayIntegration.getSyncStatus(user?.id || '');

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.5 }
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="flex items-center gap-3">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-violet-600"></div>
          <span className="text-muted-foreground">Loading your tests...</span>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="space-y-6"
    >
      {/* Header Section */}
      <motion.div variants={itemVariants} className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-3xl font-bold bg-gradient-to-r from-violet-600 via-purple-600 to-rose-600 bg-clip-text text-transparent">
            Test Management Hub
          </h2>
          <p className="text-muted-foreground mt-1">
            Manage your upcoming tests and track your exam countdown
          </p>
        </div>
        
        <div className="flex items-center gap-3">
          <NotificationCenter exams={dDayExams} />
          <TestQuickActions
            onTestAdded={loadData}
            categories={categories}
          />
        </div>
      </motion.div>

      {/* D-Day Countdown Widget */}
      <motion.div variants={itemVariants}>
        <DDayCountdownWidget 
          exams={filteredExams}
          onExamUpdated={loadData}
        />
      </motion.div>

      {/* Stats Overview */}
      <motion.div variants={itemVariants} className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card className="bg-gradient-to-br from-violet-50 to-violet-100 dark:from-violet-950/30 dark:to-violet-900/20 border-violet-200 dark:border-violet-800">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-violet-600/10">
                <Target className="h-5 w-5 text-violet-600" />
              </div>
              <div>
                <p className="text-2xl font-bold text-violet-700 dark:text-violet-400">{stats.upcoming}</p>
                <p className="text-sm text-violet-600/70 dark:text-violet-400/70">Upcoming</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-emerald-50 to-emerald-100 dark:from-emerald-950/30 dark:to-emerald-900/20 border-emerald-200 dark:border-emerald-800">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-emerald-600/10">
                <CheckCircle className="h-5 w-5 text-emerald-600" />
              </div>
              <div>
                <p className="text-2xl font-bold text-emerald-700 dark:text-emerald-400">{stats.completed}</p>
                <p className="text-sm text-emerald-600/70 dark:text-emerald-400/70">Completed</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-rose-50 to-rose-100 dark:from-rose-950/30 dark:to-rose-900/20 border-rose-200 dark:border-rose-800">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-rose-600/10">
                <AlertTriangle className="h-5 w-5 text-rose-600" />
              </div>
              <div>
                <p className="text-2xl font-bold text-rose-700 dark:text-rose-400">{stats.byPriority.critical}</p>
                <p className="text-sm text-rose-600/70 dark:text-rose-400/70">Critical</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-amber-50 to-amber-100 dark:from-amber-950/30 dark:to-amber-900/20 border-amber-200 dark:border-amber-800">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-amber-600/10">
                <BookOpen className="h-5 w-5 text-amber-600" />
              </div>
              <div>
                <p className="text-2xl font-bold text-amber-700 dark:text-amber-400">{Math.round(stats.averageStudyHours)}</p>
                <p className="text-sm text-amber-600/70 dark:text-amber-400/70">Avg Hours</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Search and Filters */}
      <motion.div variants={itemVariants} className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search tests..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <Select value={filterPriority} onValueChange={setFilterPriority}>
          <SelectTrigger className="w-full sm:w-40">
            <SelectValue placeholder="Priority" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Priorities</SelectItem>
            <SelectItem value="critical">Critical</SelectItem>
            <SelectItem value="high">High</SelectItem>
            <SelectItem value="medium">Medium</SelectItem>
            <SelectItem value="low">Low</SelectItem>
          </SelectContent>
        </Select>

        <Select value={filterStatus} onValueChange={setFilterStatus}>
          <SelectTrigger className="w-full sm:w-40">
            <SelectValue placeholder="Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="upcoming">Upcoming</SelectItem>
            <SelectItem value="in_progress">In Progress</SelectItem>
            <SelectItem value="completed">Completed</SelectItem>
            <SelectItem value="missed">Missed</SelectItem>
          </SelectContent>
        </Select>

        {/* Category Multi-Select */}
        <Popover>
          <PopoverTrigger asChild>
            <Button variant="outline" className="w-full sm:w-48 justify-between">
              {selectedCategories.length === 0
                ? "All Categories"
                : selectedCategories.length === 1
                  ? categories.find(cat => cat.id === selectedCategories[0])?.name || "Category"
                  : `${selectedCategories.length} Categories`
              }
              <ChevronDown className="h-4 w-4 opacity-50" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-56 p-0">
            <div className="p-4 space-y-2">
              <div className="flex items-center justify-between">
                <h4 className="font-medium text-sm">Categories</h4>
                {selectedCategories.length > 0 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setSelectedCategories([])}
                    className="h-auto p-0 text-xs text-muted-foreground hover:text-foreground"
                  >
                    Clear all
                  </Button>
                )}
              </div>

              <div className="space-y-2 max-h-48 overflow-y-auto">
                {categories.map((category) => (
                  <div key={category.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={category.id}
                      checked={selectedCategories.includes(category.id)}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setSelectedCategories(prev => [...prev, category.id]);
                        } else {
                          setSelectedCategories(prev => prev.filter(id => id !== category.id));
                        }
                      }}
                    />
                    <label
                      htmlFor={category.id}
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                    >
                      {category.name}
                    </label>
                  </div>
                ))}

                {categories.length === 0 && (
                  <p className="text-sm text-muted-foreground text-center py-2">
                    No categories available
                  </p>
                )}
              </div>
            </div>
          </PopoverContent>
        </Popover>
      </motion.div>

      {/* Main Content Tabs */}
      <motion.div variants={itemVariants}>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="tests">Tests</TabsTrigger>
            <TabsTrigger value="calendar">Calendar</TabsTrigger>
            <TabsTrigger value="planner">AI Planner</TabsTrigger>
            <TabsTrigger value="insights">Insights</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            {/* Quick Overview Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* Upcoming This Week */}
              <Card className="bg-gradient-to-br from-violet-50 to-purple-50 dark:from-violet-950/30 dark:to-purple-950/30 border-violet-200 dark:border-violet-800">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Calendar className="h-5 w-5 text-violet-600" />
                    This Week
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {filteredExams
                      .filter(exam => {
                        const examDate = new Date(exam.date);
                        const daysUntil = Math.ceil((examDate.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24));
                        return daysUntil <= 7 && daysUntil >= 0;
                      })
                      .slice(0, 3)
                      .map((exam) => (
                        <div key={exam.id} className="flex items-center justify-between p-2 rounded-lg bg-white/50 dark:bg-slate-800/50">
                          <span className="font-medium text-sm truncate">{exam.name}</span>
                          <Badge variant="secondary" className="text-xs">
                            {Math.ceil((new Date(exam.date).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))}d
                          </Badge>
                        </div>
                      ))}
                    {filteredExams.filter(exam => {
                      const daysUntil = Math.ceil((new Date(exam.date).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24));
                      return daysUntil <= 7 && daysUntil >= 0;
                    }).length === 0 && (
                      <p className="text-sm text-muted-foreground text-center py-4">No exams this week</p>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* High Priority */}
              <Card className="bg-gradient-to-br from-rose-50 to-red-50 dark:from-rose-950/30 dark:to-red-950/30 border-rose-200 dark:border-rose-800">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg flex items-center gap-2">
                    <AlertTriangle className="h-5 w-5 text-rose-600" />
                    High Priority
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {filteredExams
                      .filter(exam => exam.priority === 'critical' || exam.priority === 'high')
                      .slice(0, 3)
                      .map((exam) => (
                        <div key={exam.id} className="flex items-center justify-between p-2 rounded-lg bg-white/50 dark:bg-slate-800/50">
                          <span className="font-medium text-sm truncate">{exam.name}</span>
                          <Badge
                            variant="secondary"
                            className={cn(
                              "text-xs",
                              exam.priority === 'critical' && "bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400",
                              exam.priority === 'high' && "bg-orange-100 text-orange-700 dark:bg-orange-900/30 dark:text-orange-400"
                            )}
                          >
                            {exam.priority}
                          </Badge>
                        </div>
                      ))}
                    {filteredExams.filter(exam => exam.priority === 'critical' || exam.priority === 'high').length === 0 && (
                      <p className="text-sm text-muted-foreground text-center py-4">No high priority exams</p>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Study Progress */}
              <Card className="bg-gradient-to-br from-emerald-50 to-green-50 dark:from-emerald-950/30 dark:to-green-950/30 border-emerald-200 dark:border-emerald-800">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg flex items-center gap-2">
                    <BookOpen className="h-5 w-5 text-emerald-600" />
                    Study Progress
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {filteredExams
                      .filter(exam => exam.preparationData.chapters.length > 0)
                      .slice(0, 3)
                      .map((exam) => {
                        const progress = chapterProgressUtils.calculateOverallProgress(exam.preparationData.chapters);
                        return (
                          <div key={exam.id} className="space-y-2">
                            <div className="flex items-center justify-between">
                              <span className="font-medium text-sm truncate">{exam.name}</span>
                              <span className="text-xs text-muted-foreground">{progress}%</span>
                            </div>
                            <Progress value={progress} className="h-2" />
                          </div>
                        );
                      })}
                    {filteredExams.filter(exam => exam.preparationData.chapters.length > 0).length === 0 && (
                      <p className="text-sm text-muted-foreground text-center py-4">No study progress tracked</p>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="tests" className="space-y-6">
            {/* Tests Grid */}
            {filteredExams.length === 0 ? (
              <Card className="bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900/50 dark:to-slate-800/50">
                <CardContent className="flex flex-col items-center justify-center py-16">
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    className="p-6 rounded-full bg-slate-100 dark:bg-slate-800 mb-6"
                  >
                    <Target className="h-16 w-16 text-slate-400" />
                  </motion.div>
                  <h3 className="text-2xl font-bold mb-3">No tests found</h3>
                  <p className="text-muted-foreground text-center mb-6 max-w-md">
                    {searchQuery || filterPriority !== "all" || filterStatus !== "all"
                      ? "Try adjusting your filters or search query"
                      : "Add your first test to get started with exam preparation tracking"
                    }
                  </p>
                  <TestQuickActions onTestAdded={loadData} categories={categories} />
                </CardContent>
              </Card>
            ) : (
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                <AnimatePresence>
                  {filteredExams.map((exam, index) => (
                    <motion.div
                      key={exam.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      transition={{ delay: index * 0.1 }}
                    >
                      <EnhancedUpcomingTestCard
                        test={{
                          id: exam.id,
                          name: exam.name,
                          date: exam.date,
                          time: exam.time,
                          categoryId: exam.category,
                          syllabus: exam.preparationData.totalTopics,
                          isNotificationEnabled: exam.reminderSettings.enabled,
                          userId: exam.userId,
                          createdAt: exam.createdAt,
                          daysLeft: Math.ceil((new Date(exam.date).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
                        }}
                        dDayExam={exam}
                        category={categories.find(cat => cat.id === exam.category)}
                        onEdit={() => handleEditExam(exam)}
                        onDelete={() => handleDeleteExam(exam.id)}

                      />
                    </motion.div>
                  ))}
                </AnimatePresence>
              </div>
            )}
          </TabsContent>

          <TabsContent value="calendar" className="space-y-6">
            <ExamCalendarView
              exams={filteredExams}
              onExamClick={setSelectedExam}
            />
          </TabsContent>

          <TabsContent value="planner" className="space-y-6">
            <SmartStudyPlanner
              exams={filteredExams}
              selectedExam={selectedExam}
              onExamSelect={setSelectedExam}
            />
          </TabsContent>

          <TabsContent value="insights" className="space-y-6">
            <TestInsightsPanel
              exams={dDayExams}
              stats={stats}
              syncStatus={syncStatus}
            />
          </TabsContent>
        </Tabs>
      </motion.div>

      {/* Edit Dialog */}
      <EditDDayExamDialog
        open={isEditDialogOpen}
        onOpenChange={setIsEditDialogOpen}
        exam={editingExam}
        categories={categories}
        onSave={loadData}
        userId={user?.id || ''}
      />
    </motion.div>
  );
}
