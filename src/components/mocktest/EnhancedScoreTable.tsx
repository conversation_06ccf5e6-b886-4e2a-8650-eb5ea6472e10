import { useState, useMemo } from "react";
import { motion } from "framer-motion";
import { 
  Eye, 
  Calendar, 
  Target, 
  TrendingUp, 
  TrendingDown,
  Award,
  Clock,
  FileText,
  ExternalLink,
  Filter,
  SortAsc,
  SortDesc,
  CheckCircle2,
  XCircle,
  AlertCircle
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { MockTest, TestCategory } from "@/types/mockTest";
import { categoryStorage } from "@/utils/mockTestLocalStorage";
import { useSupabaseAuth } from "@/contexts/SupabaseAuthContext";
import { cn } from "@/lib/utils";

interface EnhancedScoreTableProps {
  tests: MockTest[];
  categories: TestCategory[];
  onViewDetails: (test: MockTest) => void;
}

type SortField = 'date' | 'name' | 'percentage' | 'totalMarks' | 'category';
type SortDirection = 'asc' | 'desc';

export function EnhancedScoreTable({ tests, categories, onViewDetails }: EnhancedScoreTableProps) {
  const { user } = useSupabaseAuth();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [reviewFilter, setReviewFilter] = useState<string>("all");
  const [sortField, setSortField] = useState<SortField>('date');
  const [sortDirection, setSortDirection] = useState<SortDirection>('desc');

  // Filter and sort tests
  const filteredAndSortedTests = useMemo(() => {
    let filtered = tests.filter(test => {
      // Search filter
      if (searchTerm && !test.name.toLowerCase().includes(searchTerm.toLowerCase())) {
        return false;
      }

      // Category filter
      if (selectedCategory !== "all") {
        if (selectedCategory === "uncategorized" && test.categoryId) {
          return false;
        }
        if (selectedCategory !== "uncategorized" && test.categoryId !== selectedCategory) {
          return false;
        }
      }

      // Review filter
      if (reviewFilter !== "all") {
        if (reviewFilter === "reviewed" && !test.isReviewed) {
          return false;
        }
        if (reviewFilter === "unreviewed" && test.isReviewed) {
          return false;
        }
      }

      return true;
    });

    // Sort tests
    filtered.sort((a, b) => {
      let aValue: any, bValue: any;

      switch (sortField) {
        case 'date':
          aValue = new Date(a.date).getTime();
          bValue = new Date(b.date).getTime();
          break;
        case 'name':
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
          break;
        case 'percentage':
          aValue = a.marksObtained && a.totalMarks ? (a.marksObtained / a.totalMarks) * 100 : 0;
          bValue = b.marksObtained && b.totalMarks ? (b.marksObtained / b.totalMarks) * 100 : 0;
          break;
        case 'totalMarks':
          aValue = a.totalMarks || 0;
          bValue = b.totalMarks || 0;
          break;
        case 'category':
          aValue = getCategoryName(a.categoryId) || 'Uncategorized';
          bValue = getCategoryName(b.categoryId) || 'Uncategorized';
          break;
        default:
          return 0;
      }

      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
      return 0;
    });

    return filtered;
  }, [tests, searchTerm, selectedCategory, reviewFilter, sortField, sortDirection]);

  const getCategoryName = (categoryId?: string) => {
    if (!categoryId) return null;
    return categories.find(cat => cat.id === categoryId)?.name;
  };

  const getCategoryColor = (categoryId?: string) => {
    if (!categoryId) return '#6b7280';
    return categories.find(cat => cat.id === categoryId)?.color || '#6b7280';
  };

  const getPercentageColor = (percentage: number) => {
    if (percentage >= 90) return 'text-emerald-600';
    if (percentage >= 75) return 'text-emerald-500';
    if (percentage >= 60) return 'text-yellow-600';
    if (percentage >= 40) return 'text-orange-600';
    return 'text-red-600';
  };

  const getPerformanceBadge = (percentage: number) => {
    if (percentage >= 90) return { label: 'Excellent', variant: 'default' as const, color: 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900/30 dark:text-emerald-300' };
    if (percentage >= 75) return { label: 'Good', variant: 'secondary' as const, color: 'bg-emerald-50 text-emerald-700 dark:bg-emerald-900/20 dark:text-emerald-400' };
    if (percentage >= 60) return { label: 'Average', variant: 'outline' as const, color: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300' };
    if (percentage >= 40) return { label: 'Below Average', variant: 'outline' as const, color: 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300' };
    return { label: 'Poor', variant: 'destructive' as const, color: 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300' };
  };

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };

  const SortIcon = ({ field }: { field: SortField }) => {
    if (sortField !== field) return null;
    return sortDirection === 'asc' ? 
      <SortAsc className="h-4 w-4 ml-1" /> : 
      <SortDesc className="h-4 w-4 ml-1" />;
  };

  if (!user) {
    return <div>Please log in to view test scores.</div>;
  }

  return (
    <div className="space-y-6">
      {/* Enhanced Filters */}
      <Card className="overflow-hidden border border-border shadow-xl bg-card hover:shadow-2xl hover:-translate-y-1 transition-all duration-500 group relative">
        <div className="absolute inset-0 bg-muted/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
        <CardHeader className="bg-muted/30 border-b border-border relative z-10">
          <CardTitle className="flex items-center gap-3 text-xl font-bold">
            <div className="p-2 rounded-lg bg-gray-700 shadow-lg">
              <FileText className="h-6 w-6 text-white" />
            </div>
            <span className="text-foreground">
              Test Scores & Performance
            </span>
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6 relative z-10">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <Input
                placeholder="Search tests..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full bg-white/50 border-gray-300/50 focus:border-gray-500 focus:ring-gray-500/20 dark:bg-gray-800/50 dark:border-gray-600/50 dark:focus:border-gray-400"
              />
            </div>

            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-40 bg-white/50 border-gray-300/50 dark:bg-gray-800/50 dark:border-gray-600/50">
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                <SelectItem value="uncategorized">Uncategorized</SelectItem>
                {categories.map((category) => (
                  <SelectItem key={category.id} value={category.id}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={reviewFilter} onValueChange={setReviewFilter}>
              <SelectTrigger className="w-32 bg-white/50 border-gray-300/50 dark:bg-gray-800/50 dark:border-gray-600/50">
                <SelectValue placeholder="Review Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Tests</SelectItem>
                <SelectItem value="reviewed">Reviewed</SelectItem>
                <SelectItem value="unreviewed">Unreviewed</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Results Summary */}
      <div className="flex items-center justify-between">
        <p className="text-sm text-muted-foreground">
          Showing {filteredAndSortedTests.length} of {tests.length} tests
        </p>
        {filteredAndSortedTests.length > 0 && (
          <div className="flex items-center gap-4 text-sm">
            <span>
              Average: {(filteredAndSortedTests.reduce((sum, test) => {
                if (test.marksObtained && test.totalMarks) {
                  return sum + (test.marksObtained / test.totalMarks) * 100;
                }
                return sum;
              }, 0) / filteredAndSortedTests.filter(test => test.marksObtained && test.totalMarks).length).toFixed(1)}%
            </span>
          </div>
        )}
      </div>

      {/* Table */}
      {filteredAndSortedTests.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <FileText className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">No tests found</h3>
            <p className="text-muted-foreground text-center">
              {searchTerm || selectedCategory !== "all" || reviewFilter !== "all"
                ? "Try adjusting your filters to see more results"
                : "Complete some mock tests to see your scores here"
              }
            </p>
          </CardContent>
        </Card>
      ) : (
        <Card className="overflow-hidden border border-border shadow-xl bg-card">
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <Table>
                <TableHeader className="bg-muted/30">
                  <TableRow className="border-b border-border">
                    <TableHead 
                      className="cursor-pointer hover:bg-muted/50"
                      onClick={() => handleSort('name')}
                    >
                      <div className="flex items-center">
                        Test Name
                        <SortIcon field="name" />
                      </div>
                    </TableHead>
                    <TableHead 
                      className="cursor-pointer hover:bg-muted/50"
                      onClick={() => handleSort('date')}
                    >
                      <div className="flex items-center">
                        Date
                        <SortIcon field="date" />
                      </div>
                    </TableHead>
                    <TableHead 
                      className="cursor-pointer hover:bg-muted/50"
                      onClick={() => handleSort('category')}
                    >
                      <div className="flex items-center">
                        Category
                        <SortIcon field="category" />
                      </div>
                    </TableHead>
                    <TableHead 
                      className="cursor-pointer hover:bg-muted/50 text-center"
                      onClick={() => handleSort('percentage')}
                    >
                      <div className="flex items-center justify-center">
                        Score
                        <SortIcon field="percentage" />
                      </div>
                    </TableHead>
                    <TableHead className="text-center">Performance</TableHead>
                    <TableHead className="text-center">Review Status</TableHead>
                    <TableHead className="text-center">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredAndSortedTests.map((test, index) => {
                    const percentage = test.marksObtained && test.totalMarks 
                      ? (test.marksObtained / test.totalMarks) * 100 
                      : 0;
                    const performanceBadge = getPerformanceBadge(percentage);
                    const categoryName = getCategoryName(test.categoryId);
                    const categoryColor = getCategoryColor(test.categoryId);

                    return (
                      <motion.tr
                        key={test.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.05 }}
                        className="hover:bg-muted/50"
                      >
                        <TableCell>
                          <div>
                            <div className="font-medium line-clamp-1">{test.name}</div>
                            {test.testPaperUrl && (
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-auto p-0 text-xs text-blue-600 hover:text-blue-800"
                                onClick={() => window.open(test.testPaperUrl, '_blank')}
                              >
                                <ExternalLink className="h-3 w-3 mr-1" />
                                View Paper
                              </Button>
                            )}
                          </div>
                        </TableCell>
                        
                        <TableCell>
                          <div className="flex items-center gap-2 text-sm">
                            <Calendar className="h-4 w-4 text-muted-foreground" />
                            {new Date(test.date).toLocaleDateString()}
                          </div>
                        </TableCell>
                        
                        <TableCell>
                          {categoryName ? (
                            <Badge 
                              variant="outline" 
                              className="text-xs"
                              style={{ 
                                borderColor: categoryColor,
                                color: categoryColor 
                              }}
                            >
                              {categoryName}
                            </Badge>
                          ) : (
                            <Badge variant="outline" className="text-xs text-muted-foreground">
                              Uncategorized
                            </Badge>
                          )}
                        </TableCell>
                        
                        <TableCell className="text-center">
                          {test.marksObtained !== undefined && test.totalMarks !== undefined ? (
                            <div className="space-y-1">
                              <div className="font-semibold">
                                {test.marksObtained}/{test.totalMarks}
                              </div>
                              <div className={cn("text-sm font-medium", getPercentageColor(percentage))}>
                                {percentage.toFixed(1)}%
                              </div>
                              <Progress value={percentage} className="h-1 w-16 mx-auto" />
                            </div>
                          ) : (
                            <span className="text-muted-foreground text-sm">Not recorded</span>
                          )}
                        </TableCell>
                        
                        <TableCell className="text-center">
                          {test.marksObtained !== undefined && test.totalMarks !== undefined ? (
                            <Badge className={performanceBadge.color}>
                              {performanceBadge.label}
                            </Badge>
                          ) : (
                            <Badge variant="outline" className="text-muted-foreground">
                              N/A
                            </Badge>
                          )}
                        </TableCell>
                        
                        <TableCell className="text-center">
                          <div className="flex items-center justify-center">
                            {test.isReviewed ? (
                              <div className="flex items-center gap-1 text-green-600">
                                <CheckCircle2 className="h-4 w-4" />
                                <span className="text-xs">Reviewed</span>
                              </div>
                            ) : (
                              <div className="flex items-center gap-1 text-orange-600">
                                <AlertCircle className="h-4 w-4" />
                                <span className="text-xs">Pending</span>
                              </div>
                            )}
                          </div>
                        </TableCell>
                        
                        <TableCell className="text-center">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => onViewDetails(test)}
                            className="gap-2"
                          >
                            <Eye className="h-4 w-4" />
                            Details
                          </Button>
                        </TableCell>
                      </motion.tr>
                    );
                  })}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
