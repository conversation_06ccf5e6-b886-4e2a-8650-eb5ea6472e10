import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { 
  Plus, 
  Edit2, 
  Trash2, 
  Tag, 
  Palette,
  Save,
  X,
  FolderO<PERSON>,
  Hash
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { TestCategory } from "@/types/mockTest";
import { categoryStorage } from "@/utils/mockTestLocalStorage";
import { useSupabaseAuth } from "@/contexts/SupabaseAuthContext";
import { toast } from "@/components/ui/use-toast";
import { cn } from "@/lib/utils";

interface CategoryManagerProps {
  onCategoryChange?: () => void;
  selectedCategoryId?: string;
  onCategorySelect?: (categoryId: string | null) => void;
  showSelector?: boolean;
}

const PRESET_COLORS = [
  "#ef4444", // red
  "#f97316", // orange
  "#eab308", // yellow
  "#22c55e", // green
  "#06b6d4", // cyan
  "#3b82f6", // blue
  "#8b5cf6", // violet
  "#ec4899", // pink
  "#6b7280", // gray
  "#84cc16", // lime
];

export function CategoryManager({ 
  onCategoryChange, 
  selectedCategoryId, 
  onCategorySelect,
  showSelector = false 
}: CategoryManagerProps) {
  const { user } = useSupabaseAuth();
  const [categories, setCategories] = useState<TestCategory[]>([]);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [editingCategory, setEditingCategory] = useState<TestCategory | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

  // Form state
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    color: PRESET_COLORS[0],
  });

  // Load categories on component mount
  useEffect(() => {
    if (user) {
      loadCategories();
    }
  }, [user]);

  const loadCategories = () => {
    if (!user) return;
    const cats = categoryStorage.getAll(user.id);
    setCategories(cats);
  };

  const resetForm = () => {
    setFormData({
      name: "",
      description: "",
      color: PRESET_COLORS[0],
    });
  };

  const handleAddCategory = () => {
    if (!user || !formData.name.trim()) {
      toast({
        title: "Error",
        description: "Please enter a category name",
        variant: "destructive",
      });
      return;
    }

    try {
      categoryStorage.create({
        name: formData.name.trim(),
        description: formData.description.trim() || undefined,
        color: formData.color,
        userId: user.id,
      });

      loadCategories();
      setIsAddDialogOpen(false);
      resetForm();
      onCategoryChange?.();

      toast({
        title: "Success",
        description: "Category created successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create category",
        variant: "destructive",
      });
    }
  };

  const handleEditCategory = () => {
    if (!editingCategory || !formData.name.trim()) {
      toast({
        title: "Error",
        description: "Please enter a category name",
        variant: "destructive",
      });
      return;
    }

    try {
      categoryStorage.update(editingCategory.id, {
        name: formData.name.trim(),
        description: formData.description.trim() || undefined,
        color: formData.color,
      });

      loadCategories();
      setIsEditDialogOpen(false);
      setEditingCategory(null);
      resetForm();
      onCategoryChange?.();

      toast({
        title: "Success",
        description: "Category updated successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update category",
        variant: "destructive",
      });
    }
  };

  const handleDeleteCategory = (categoryId: string) => {
    try {
      categoryStorage.delete(categoryId);
      loadCategories();
      onCategoryChange?.();

      // If the deleted category was selected, clear selection
      if (selectedCategoryId === categoryId) {
        onCategorySelect?.(null);
      }

      toast({
        title: "Success",
        description: "Category deleted successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete category",
        variant: "destructive",
      });
    }
  };

  const openEditDialog = (category: TestCategory) => {
    setEditingCategory(category);
    setFormData({
      name: category.name,
      description: category.description || "",
      color: category.color || PRESET_COLORS[0],
    });
    setIsEditDialogOpen(true);
  };

  if (!user) {
    return null;
  }

  return (
    <div className="space-y-6">
      {/* Category Selector (if enabled) */}
      {showSelector && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Tag className="h-5 w-5" />
              Filter by Category
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Select
              value={selectedCategoryId || "all"}
              onValueChange={(value) => onCategorySelect?.(value === "all" ? null : value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                <SelectItem value="uncategorized">Uncategorized</SelectItem>
                {categories.map((category) => (
                  <SelectItem key={category.id} value={category.id}>
                    <div className="flex items-center gap-2">
                      <div 
                        className="w-3 h-3 rounded-full" 
                        style={{ backgroundColor: category.color }}
                      />
                      {category.name}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </CardContent>
        </Card>
      )}

      {/* Enhanced Category Management */}
      <Card className="overflow-hidden border border-border shadow-xl bg-card hover:shadow-2xl hover:-translate-y-1 transition-all duration-500 group relative">
        <div className="absolute inset-0 bg-muted/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
        <CardHeader className="bg-muted/30 border-b border-border relative z-10">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-3 text-xl font-bold">
              <div className="p-2 rounded-lg bg-rose-600 shadow-lg">
                <FolderOpen className="h-6 w-6 text-white" />
              </div>
              <span className="text-foreground">
                Test Categories
              </span>
            </CardTitle>
            <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
              <DialogTrigger asChild>
                <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                  <Button
                    onClick={resetForm}
                    size="sm"
                    className="gap-2 bg-rose-600 hover:bg-rose-700 text-white shadow-lg hover:shadow-xl transition-all duration-300"
                  >
                    <Plus className="h-4 w-4" />
                    Add Category
                  </Button>
                </motion.div>
              </DialogTrigger>
              <DialogContent className="max-w-md">
                <DialogHeader>
                  <DialogTitle>Create New Category</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="category-name">Category Name *</Label>
                    <Input
                      id="category-name"
                      value={formData.name}
                      onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="e.g., JEE Main, NEET, Practice Tests"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="category-description">Description</Label>
                    <Textarea
                      id="category-description"
                      value={formData.description}
                      onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                      placeholder="Optional description for this category"
                      rows={3}
                    />
                  </div>

                  <div>
                    <Label>Color</Label>
                    <div className="grid grid-cols-5 gap-2 mt-2">
                      {PRESET_COLORS.map((color) => (
                        <button
                          key={color}
                          type="button"
                          className={cn(
                            "w-8 h-8 rounded-full border-2 transition-all",
                            formData.color === color 
                              ? "border-gray-900 scale-110" 
                              : "border-gray-300 hover:scale-105"
                          )}
                          style={{ backgroundColor: color }}
                          onClick={() => setFormData(prev => ({ ...prev, color }))}
                        />
                      ))}
                    </div>
                  </div>

                  <div className="flex gap-2 pt-4">
                    <Button onClick={handleAddCategory} className="flex-1">
                      Create Category
                    </Button>
                    <Button 
                      variant="outline" 
                      onClick={() => setIsAddDialogOpen(false)}
                      className="flex-1"
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          {categories.length === 0 ? (
            <div className="text-center py-12">
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5 }}
                className="p-6 rounded-full bg-rose-100 dark:bg-rose-900/30 mx-auto mb-6 w-fit"
              >
                <Hash className="h-16 w-16 text-rose-600 dark:text-rose-400" />
              </motion.div>
              <h3 className="text-2xl font-bold mb-3 text-foreground">No categories yet</h3>
              <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                Create categories to organize your mock tests by type, exam, or any other criteria
              </p>
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Button
                  onClick={() => setIsAddDialogOpen(true)}
                  className="gap-2 bg-rose-600 hover:bg-rose-700 text-white shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  <Plus className="h-4 w-4" />
                  Create Your First Category
                </Button>
              </motion.div>
            </div>
          ) : (
            <div className="grid gap-3 sm:grid-cols-2 lg:grid-cols-3">
              {categories.map((category, index) => (
                <motion.div
                  key={category.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Card className="h-full overflow-hidden border border-border shadow-lg bg-card hover:shadow-xl hover:-translate-y-1 transition-all duration-300 group relative">
                    <div
                      className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl"
                      style={{ backgroundColor: `${category.color}10` }}
                    />
                    <CardContent className="p-5 relative z-10">
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex items-center gap-3">
                          <div
                            className="w-5 h-5 rounded-full shadow-md border-2 border-white dark:border-gray-800"
                            style={{ backgroundColor: category.color }}
                          />
                          <h4 className="font-bold line-clamp-1 text-gray-900 dark:text-gray-100">{category.name}</h4>
                        </div>
                        <div className="flex gap-2">
                          <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => openEditDialog(category)}
                              className="h-8 w-8 rounded-full hover:bg-violet-100 hover:text-violet-600 transition-colors duration-200 dark:hover:bg-violet-900/30 dark:hover:text-violet-400"
                            >
                              <Edit2 className="h-3.5 w-3.5" />
                            </Button>
                          </motion.div>
                          <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDeleteCategory(category.id)}
                              className="h-8 w-8 rounded-full hover:bg-red-100 hover:text-red-600 transition-colors duration-200 dark:hover:bg-red-900/30 dark:hover:text-red-400"
                            >
                              <Trash2 className="h-3.5 w-3.5" />
                            </Button>
                          </motion.div>
                        </div>
                      </div>

                      {category.description && (
                        <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2 mb-4">
                          {category.description}
                        </p>
                      )}

                      <div className="pt-3 border-t border-gray-200/50 dark:border-gray-700/50">
                        <Badge
                          className="text-xs shadow-sm"
                          style={{
                            backgroundColor: `${category.color}15`,
                            color: category.color,
                            borderColor: `${category.color}30`
                          }}
                        >
                          Created {new Date(category.createdAt).toLocaleDateString()}
                        </Badge>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Edit Category</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="edit-category-name">Category Name *</Label>
              <Input
                id="edit-category-name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="e.g., JEE Main, NEET, Practice Tests"
              />
            </div>
            
            <div>
              <Label htmlFor="edit-category-description">Description</Label>
              <Textarea
                id="edit-category-description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Optional description for this category"
                rows={3}
              />
            </div>

            <div>
              <Label>Color</Label>
              <div className="grid grid-cols-5 gap-2 mt-2">
                {PRESET_COLORS.map((color) => (
                  <button
                    key={color}
                    type="button"
                    className={cn(
                      "w-8 h-8 rounded-full border-2 transition-all",
                      formData.color === color 
                        ? "border-gray-900 scale-110" 
                        : "border-gray-300 hover:scale-105"
                    )}
                    style={{ backgroundColor: color }}
                    onClick={() => setFormData(prev => ({ ...prev, color }))}
                  />
                ))}
              </div>
            </div>

            <div className="flex gap-2 pt-4">
              <Button onClick={handleEditCategory} className="flex-1">
                Update Category
              </Button>
              <Button 
                variant="outline" 
                onClick={() => {
                  setIsEditDialogOpen(false);
                  setEditingCategory(null);
                  resetForm();
                }}
                className="flex-1"
              >
                Cancel
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
