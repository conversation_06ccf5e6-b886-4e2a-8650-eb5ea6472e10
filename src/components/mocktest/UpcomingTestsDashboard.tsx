import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { 
  Calendar, 
  Clock, 
  AlertTriangle, 
  Plus, 
  Edit2, 
  Trash2, 
  ExternalLink,
  BookOpen,
  Target,
  Bell,
  BellOff
} from "lucide-react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { UpcomingTest, TestCategory } from "@/types/mockTest";
import { upcomingTestStorage, categoryStorage, dDayIntegration, urlUtils } from "@/utils/mockTestLocalStorage";
import { useSupabaseAuth } from "@/contexts/SupabaseAuthContext";
import { toast } from "@/components/ui/use-toast";

interface UpcomingTestsDashboardProps {
  onCreateMockTest?: (upcomingTest: UpcomingTest) => void;
}

export function UpcomingTestsDashboard({ onCreateMockTest }: UpcomingTestsDashboardProps) {
  const { user } = useSupabaseAuth();
  const [upcomingTests, setUpcomingTests] = useState<UpcomingTest[]>([]);
  const [categories, setCategories] = useState<TestCategory[]>([]);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [editingTest, setEditingTest] = useState<UpcomingTest | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

  // Form state for adding/editing tests
  const [formData, setFormData] = useState({
    name: "",
    date: new Date(),
    time: "09:00",
    categoryId: "",
    syllabus: [] as string[],
    testPaperUrl: "",
    isNotificationEnabled: true,
  });

  // Load data on component mount
  useEffect(() => {
    if (user) {
      loadUpcomingTests();
      loadCategories();
    }
  }, [user]);

  const loadUpcomingTests = () => {
    if (!user) return;
    const tests = upcomingTestStorage.getUpcoming(user.id, 60); // Next 60 days
    setUpcomingTests(tests);
  };

  const loadCategories = () => {
    if (!user) return;
    const cats = categoryStorage.getAll(user.id);
    setCategories(cats);
  };

  const resetForm = () => {
    setFormData({
      name: "",
      date: new Date(),
      time: "09:00",
      categoryId: "",
      syllabus: [],
      testPaperUrl: "",
      isNotificationEnabled: true,
    });
  };

  const handleAddTest = async () => {
    if (!user || !formData.name || !formData.date) {
      toast({
        title: "Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    // Validate URL if provided
    if (formData.testPaperUrl && !urlUtils.isValidUrl(urlUtils.formatUrl(formData.testPaperUrl))) {
      toast({
        title: "Invalid URL",
        description: "Please enter a valid URL for the test paper",
        variant: "destructive",
      });
      return;
    }

    try {
      const newTest = upcomingTestStorage.create({
        name: formData.name,
        date: format(formData.date, "yyyy-MM-dd"),
        time: formData.time,
        categoryId: formData.categoryId || undefined,
        syllabus: formData.syllabus,
        testPaperUrl: formData.testPaperUrl ? urlUtils.formatUrl(formData.testPaperUrl) : undefined,
        isNotificationEnabled: formData.isNotificationEnabled,
        userId: user.id,
      });

      // Sync with D-Day (ExamCountdown) - now stored locally
      try {
        dDayIntegration.syncWithDDay(newTest);

        toast({
          title: "Success",
          description: "Upcoming test added and synced with D-Day countdown",
        });
      } catch (dDayError) {
        console.warn("Failed to sync with D-Day:", dDayError);
        toast({
          title: "Partial Success",
          description: "Test added but D-Day sync failed. You can manually add it to D-Day.",
        });
      }

      loadUpcomingTests();
      setIsAddDialogOpen(false);
      resetForm();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add upcoming test",
        variant: "destructive",
      });
    }
  };

  const handleEditTest = async () => {
    if (!editingTest || !formData.name || !formData.date) {
      toast({
        title: "Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    try {
      const updatedTest = upcomingTestStorage.update(editingTest.id, {
        name: formData.name,
        date: format(formData.date, "yyyy-MM-dd"),
        time: formData.time,
        categoryId: formData.categoryId || undefined,
        syllabus: formData.syllabus,
        testPaperUrl: formData.testPaperUrl || undefined,
        isNotificationEnabled: formData.isNotificationEnabled,
      });

      // Sync with D-Day - now stored locally
      if (updatedTest) {
        try {
          dDayIntegration.syncWithDDay(updatedTest);
        } catch (dDayError) {
          console.warn("Failed to sync update with D-Day:", dDayError);
        }
      }

      loadUpcomingTests();
      setIsEditDialogOpen(false);
      setEditingTest(null);
      resetForm();

      toast({
        title: "Success",
        description: "Test updated successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update test",
        variant: "destructive",
      });
    }
  };

  const handleDeleteTest = async (testId: string) => {
    const test = upcomingTests.find(t => t.id === testId);
    if (!test) return;

    // Show confirmation dialog
    const confirmed = window.confirm(
      `Are you sure you want to delete "${test.name}"? This action cannot be undone.`
    );

    if (!confirmed) return;

    try {
      upcomingTestStorage.delete(testId);

      // Sync with D-Day - now stored locally
      if (user?.id) {
        try {
          dDayIntegration.removeDDayExam(user.id, testId);
        } catch (dDayError) {
          console.warn("Failed to sync deletion with D-Day:", dDayError);
        }
      }

      loadUpcomingTests();
      toast({
        title: "Success",
        description: "Test deleted successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete test",
        variant: "destructive",
      });
    }
  };

  const openEditDialog = (test: UpcomingTest) => {
    setEditingTest(test);
    setFormData({
      name: test.name,
      date: new Date(test.date),
      time: test.time || "09:00",
      categoryId: test.categoryId || "",
      syllabus: test.syllabus || [],
      testPaperUrl: test.testPaperUrl || "",
      isNotificationEnabled: test.isNotificationEnabled,
    });
    setIsEditDialogOpen(true);
  };

  const getDaysLeftColor = (daysLeft: number) => {
    if (daysLeft <= 3) return "text-red-600 bg-red-50 border-red-200";
    if (daysLeft <= 7) return "text-orange-600 bg-orange-50 border-orange-200";
    if (daysLeft <= 14) return "text-yellow-600 bg-yellow-50 border-yellow-200";
    return "text-green-600 bg-green-50 border-green-200";
  };

  const getCategoryName = (categoryId?: string) => {
    if (!categoryId) return null;
    const category = categories.find(cat => cat.id === categoryId);
    return category?.name;
  };

  if (!user) {
    return (
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          Please log in to view upcoming tests.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Enhanced Header */}
      <div className="flex items-center justify-between mb-8">
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5 }}
        >
          <h2 className="text-3xl font-bold tracking-tight text-foreground">
            Upcoming Tests
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Stay prepared with your scheduled tests and important deadlines
          </p>
        </motion.div>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Button
                onClick={resetForm}
                className="gap-2 bg-emerald-600 hover:bg-emerald-700 text-white shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <Plus className="h-4 w-4" />
                Add Test
              </Button>
            </motion.div>
          </DialogTrigger>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Add Upcoming Test</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="test-name">Test Name *</Label>
                <Input
                  id="test-name"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="Enter test name"
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Date *</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full justify-start text-left font-normal",
                          !formData.date && "text-muted-foreground"
                        )}
                      >
                        <Calendar className="mr-2 h-4 w-4" />
                        {formData.date ? format(formData.date, "PPP") : "Pick a date"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <CalendarComponent
                        mode="single"
                        selected={formData.date}
                        onSelect={(date) => date && setFormData(prev => ({ ...prev, date }))}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>
                
                <div>
                  <Label htmlFor="test-time">Time</Label>
                  <Input
                    id="test-time"
                    type="time"
                    value={formData.time}
                    onChange={(e) => setFormData(prev => ({ ...prev, time: e.target.value }))}
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="category">Category</Label>
                <Select
                  value={formData.categoryId}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, categoryId: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">No category</SelectItem>
                    {categories.map((category) => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="test-url">Test Paper URL</Label>
                <Input
                  id="test-url"
                  value={formData.testPaperUrl}
                  onChange={(e) => setFormData(prev => ({ ...prev, testPaperUrl: e.target.value }))}
                  placeholder="https://example.com/test.pdf"
                />
              </div>

              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="notifications"
                  checked={formData.isNotificationEnabled}
                  onChange={(e) => setFormData(prev => ({ ...prev, isNotificationEnabled: e.target.checked }))}
                />
                <Label htmlFor="notifications">Enable notifications</Label>
              </div>

              <div className="flex gap-2 pt-4">
                <Button onClick={handleAddTest} className="flex-1">
                  Add Test
                </Button>
                <Button 
                  variant="outline" 
                  onClick={() => setIsAddDialogOpen(false)}
                  className="flex-1"
                >
                  Cancel
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Enhanced Upcoming Tests Grid */}
      {upcomingTests.length === 0 ? (
        <Card className="overflow-hidden border border-border shadow-xl bg-card">
          <CardContent className="flex flex-col items-center justify-center py-16">
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}
              className="p-6 rounded-full bg-emerald-100 dark:bg-emerald-900/30 mb-6"
            >
              <Calendar className="h-16 w-16 text-emerald-600 dark:text-emerald-400" />
            </motion.div>
            <h3 className="text-2xl font-bold mb-3 text-foreground">No upcoming tests</h3>
            <p className="text-muted-foreground text-center mb-6 max-w-md">
              Add your upcoming tests to stay organized and prepared for your exams
            </p>
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Button
                onClick={() => setIsAddDialogOpen(true)}
                className="gap-2 bg-emerald-600 hover:bg-emerald-700 text-white shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <Plus className="h-4 w-4" />
                Add Your First Test
              </Button>
            </motion.div>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {upcomingTests.map((test, index) => (
            <motion.div
              key={test.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card className="h-full overflow-hidden border border-border shadow-xl bg-card hover:shadow-2xl hover:-translate-y-2 transition-all duration-500 group relative">
                <div className="absolute inset-0 bg-muted/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

                <CardHeader className="pb-4 relative z-10">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <CardTitle className="text-lg line-clamp-2 font-bold text-foreground transition-all duration-300">
                        {test.name}
                      </CardTitle>
                      <div className="flex items-center gap-2 mt-3">
                        {getCategoryName(test.categoryId) && (
                          <Badge className="bg-violet-600 text-white shadow-md">
                            {getCategoryName(test.categoryId)}
                          </Badge>
                        )}
                        <Badge className="text-xs bg-emerald-600 text-white shadow-md">
                          <Clock className="h-3 w-3 mr-1" />
                          D-Day Synced
                        </Badge>
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => openEditDialog(test)}
                          className="h-8 w-8 rounded-full hover:bg-violet-100 hover:text-violet-600 transition-colors duration-200 dark:hover:bg-violet-900/30 dark:hover:text-violet-400"
                        >
                          <Edit2 className="h-4 w-4" />
                        </Button>
                      </motion.div>
                      <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteTest(test.id)}
                          className="h-8 w-8 rounded-full hover:bg-red-100 hover:text-red-600 transition-colors duration-200 dark:hover:bg-red-900/30 dark:hover:text-red-400"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </motion.div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4 relative z-10">
                  <div className="flex items-center gap-3 text-sm p-3 rounded-lg bg-muted/50">
                    <div className="p-1.5 rounded-lg bg-emerald-600 shadow-md">
                      <Calendar className="h-4 w-4 text-white" />
                    </div>
                    <div>
                      <p className="font-semibold text-foreground">
                        {format(new Date(test.date), "PPP")}
                      </p>
                      {test.time && (
                        <p className="text-xs text-gray-600 dark:text-gray-400 flex items-center gap-1 mt-1">
                          <Clock className="h-3 w-3" />
                          {test.time}
                        </p>
                      )}
                    </div>
                  </div>

                  {test.daysLeft !== undefined && (
                    <motion.div
                      initial={{ scale: 0.9, opacity: 0 }}
                      animate={{ scale: 1, opacity: 1 }}
                      transition={{ delay: 0.2 }}
                    >
                      <div className={cn(
                        "inline-flex items-center gap-2 px-4 py-2 rounded-full text-sm font-semibold shadow-md",
                        getDaysLeftColor(test.daysLeft)
                      )}>
                        <Target className="h-4 w-4" />
                        {test.daysLeft === 0 ? "Today!" :
                         test.daysLeft === 1 ? "Tomorrow" :
                         `${test.daysLeft} days left`}
                      </div>
                    </motion.div>
                  )}

                  {test.testPaperUrl && (
                    <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full gap-2 bg-violet-600 text-white hover:bg-violet-700 shadow-md hover:shadow-lg transition-all duration-300"
                        onClick={() => window.open(test.testPaperUrl, '_blank')}
                      >
                        <ExternalLink className="h-4 w-4" />
                        View Test Paper
                      </Button>
                    </motion.div>
                  )}

                  <div className="flex gap-3 pt-4 border-t border-gray-200/30 dark:border-gray-700/30">
                    {onCreateMockTest && (
                      <motion.div className="flex-1" whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                        <Button
                          variant="outline"
                          size="sm"
                          className="w-full gap-2 bg-emerald-600 text-white hover:bg-emerald-700 shadow-md hover:shadow-lg transition-all duration-300"
                          onClick={() => {
                            if (test) {
                              onCreateMockTest(test);
                            }
                          }}
                        >
                          <BookOpen className="h-4 w-4" />
                          Take Test
                        </Button>
                      </motion.div>
                    )}
                    <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-9 w-9 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
                        onClick={() => {
                          upcomingTestStorage.update(test.id, {
                            isNotificationEnabled: !test.isNotificationEnabled
                          });
                          loadUpcomingTests();
                        }}
                      >
                        {test.isNotificationEnabled ? (
                          <Bell className="h-4 w-4 text-emerald-600 dark:text-emerald-400" />
                        ) : (
                          <BellOff className="h-4 w-4 text-gray-400 dark:text-gray-500" />
                        )}
                      </Button>
                    </motion.div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      )}

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Edit Test</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            {/* Same form fields as add dialog */}
            <div>
              <Label htmlFor="edit-test-name">Test Name *</Label>
              <Input
                id="edit-test-name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Enter test name"
              />
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label>Date *</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !formData.date && "text-muted-foreground"
                      )}
                    >
                      <Calendar className="mr-2 h-4 w-4" />
                      {formData.date ? format(formData.date, "PPP") : "Pick a date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <CalendarComponent
                      mode="single"
                      selected={formData.date}
                      onSelect={(date) => date && setFormData(prev => ({ ...prev, date }))}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
              
              <div>
                <Label htmlFor="edit-test-time">Time</Label>
                <Input
                  id="edit-test-time"
                  type="time"
                  value={formData.time}
                  onChange={(e) => setFormData(prev => ({ ...prev, time: e.target.value }))}
                />
              </div>
            </div>

            <div>
              <Label htmlFor="edit-category">Category</Label>
              <Select
                value={formData.categoryId}
                onValueChange={(value) => setFormData(prev => ({ ...prev, categoryId: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">No category</SelectItem>
                  {categories.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="edit-test-url">Test Paper URL</Label>
              <Input
                id="edit-test-url"
                value={formData.testPaperUrl}
                onChange={(e) => setFormData(prev => ({ ...prev, testPaperUrl: e.target.value }))}
                placeholder="https://example.com/test.pdf"
              />
            </div>

            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="edit-notifications"
                checked={formData.isNotificationEnabled}
                onChange={(e) => setFormData(prev => ({ ...prev, isNotificationEnabled: e.target.checked }))}
              />
              <Label htmlFor="edit-notifications">Enable notifications</Label>
            </div>

            <div className="flex gap-2 pt-4">
              <Button onClick={handleEditTest} className="flex-1">
                Update Test
              </Button>
              <Button 
                variant="outline" 
                onClick={() => {
                  setIsEditDialogOpen(false);
                  setEditingTest(null);
                  resetForm();
                }}
                className="flex-1"
              >
                Cancel
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
